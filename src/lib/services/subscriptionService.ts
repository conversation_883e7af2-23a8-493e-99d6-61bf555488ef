import { supabase } from '$lib/services/supabase/supabaseClient';
import { ACTIVE_STATUSES, PAST_DUE_GRACE_PERIOD_DAYS } from '$lib/config/features';

export interface SubscriptionStatus {
  isActive: boolean;
  status: string | null;
  plan?: string;
  expiresAt?: Date;
  isInGracePeriod?: boolean;
  stripeCustomerId?: string;
}

/**
 * Check if a user has an active subscription
 * Handles past_due with grace period
 */
export async function checkSubscriptionStatus(userId?: string): Promise<boolean> {
    if (!userId) return false;
  
    try {
        const { data: subscriptions, error } = await supabase
            .from('user_subscriptions')
            .select('status, updated_at')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .limit(1);
    
        if (error) {
            // Database error
            console.warn('Subscription check error:', error.message);
            return false;
        }
    
        const data = subscriptions?.[0];
        if (!data || !data.status) return false;
    
        // Check if status grants access
        if (ACTIVE_STATUSES.includes(data.status)) {
            // Special handling for past_due with grace period
            if (data.status === 'past_due' && data.updated_at) {
                const gracePeriodEnd = new Date(data.updated_at);
                gracePeriodEnd.setDate(gracePeriodEnd.getDate() + PAST_DUE_GRACE_PERIOD_DAYS);
        
                return new Date() < gracePeriodEnd;
            }
      
            return true;
        }
    
        return false;
    } catch (error) {
        console.error('Error checking subscription status:', error);
        // Fail closed - no access on error
        return false;
    }
}

/**
 * Get detailed subscription information for a user
 */
export async function getSubscriptionDetails(userId?: string): Promise<SubscriptionStatus> {
    if (!userId) {
        return { isActive: false, status: null };
    }
  
    try {
    // Get subscription data
        const { data: subscriptions, error: subError } = await supabase
            .from('user_subscriptions')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .limit(1);
    
        const subscription = subscriptions?.[0];
    
        if (subError || !subscription) {
            // Also check if user has stripe customer ID (might be setting up subscription)
            const { data: profile } = await supabase
                .from('profiles')
                .select('stripe_customer_id')
                .eq('id', userId)
                .single();
      
            return {
                isActive: false,
                status: null,
                stripeCustomerId: profile?.stripe_customer_id || undefined
            };
        }
    
        const isActive = await checkSubscriptionStatus(userId);
    
        let isInGracePeriod = false;
        if (subscription.status === 'past_due' && subscription.updated_at) {
            const gracePeriodEnd = new Date(subscription.updated_at);
            gracePeriodEnd.setDate(gracePeriodEnd.getDate() + PAST_DUE_GRACE_PERIOD_DAYS);
            isInGracePeriod = new Date() < gracePeriodEnd;
        }
    
        return {
            isActive,
            status: subscription.status,
            plan: subscription.subscription_id, // This will be the Stripe subscription ID
            expiresAt: subscription.current_period_end ? new Date(subscription.current_period_end) : undefined,
            isInGracePeriod,
            stripeCustomerId: undefined // We don't expose this in details
        };
    } catch (error) {
        console.error('Error getting subscription details:', error);
        return { isActive: false, status: null };
    }
}

/**
 * Create a Stripe checkout session for subscription
 * Gracefully handles edge function availability
 */
export async function createCheckoutSession(priceId: string) {
    try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session || !session.user) throw new Error('Not authenticated');

        // Detect if we're running in Capacitor
        const isCapacitor = (window as any).Capacitor !== undefined;
console.log('**********************isCapacitor?', isCapacitor)
        const { data, error } = await supabase.functions.invoke('create-checkout-session', {
            body: {
                priceId,
                userId: session.user.id,
                userEmail: session.user.email,
                isCapacitor
            },
            headers: {
                Authorization: `Bearer ${session.access_token}`
            }
        });
    
        if (error) {
            // Edge function might not be deployed
            console.error('Checkout session error:', error);
            return {
                error: 'Payment system is currently unavailable. Please try again later.',
                url: null
            };
        }
    
        return {
            error: null,
            url: data?.url || null
        };
    } catch (error) {
        console.error('Error creating checkout session:', error);
        return {
            error: 'Unable to start checkout process. Please ensure you are logged in and try again.',
            url: null
        };
    }
}

/**
 * Create a Stripe customer portal session for subscription management
 */
export async function createPortalSession(returnUrl: string) {
    try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session || !session.user) throw new Error('Not authenticated');
    
        const { data, error } = await supabase.functions.invoke('create-portal-session', {
            body: {
                returnUrl,
                userId: session.user.id
            },
            headers: {
                Authorization: `Bearer ${session.access_token}`
            }
        });
    
        if (error) {
            console.error('Portal session error:', error);
            return {
                error: 'Subscription management is currently unavailable. Please try again later.',
                url: null
            };
        }
    
        return {
            error: null,
            url: data?.url || null
        };
    } catch (error) {
        console.error('Error creating portal session:', error);
        return {
            error: 'Unable to access subscription management. Please ensure you are logged in and try again.',
            url: null
        };
    }
}

/**
 * Verify a user has completed payment after checkout
 * Used on the success page to ensure subscription is active
 */
export async function verifyPaymentCompletion(userId: string, maxAttempts = 10, delayMs = 2000): Promise<boolean> {
    for (let i = 0; i < maxAttempts; i++) {
        const isActive = await checkSubscriptionStatus(userId);
        if (isActive) return true;
    
        // Wait before next attempt (webhook might still be processing)
        if (i < maxAttempts - 1) {
            await new Promise(resolve => setTimeout(resolve, delayMs));
        }
    }
  
    return false;
}